{"compilerOptions": {"esModuleInterop": true, "forceConsistentCasingInFileNames": true, "composite": false, "declaration": true, "declarationMap": true, "inlineSources": false, "isolatedModules": true, "moduleResolution": "node", "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "skipLibCheck": true, "strict": true, "checkJs": false}, "include": ["src"], "exclude": ["dist", "node_modules"]}