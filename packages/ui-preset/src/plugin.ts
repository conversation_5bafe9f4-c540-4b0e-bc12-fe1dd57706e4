import plugin from "tailwindcss/plugin";
import { FONT_FAMILY_MONO, FONT_FAMILY_SANS } from "./constants";
import { typography } from "./typography";

export default plugin(
  function safUi({ addBase, addComponents }) {
    // Add base border color using new CSS variable
    addBase({
      "*": {
        borderColor: "hsl(var(--border))",
      },
    });

    // Add typography components
    addComponents(typography);
  },
  {
    theme: {
      extend: {
        colors: {
          border: "hsl(var(--border))",
          input: "hsl(var(--input))",
          ring: "hsl(var(--ring))",
          background: "hsl(var(--background))",
          foreground: "hsl(var(--foreground))",
          primary: {
            DEFAULT: "hsl(var(--primary))",
            foreground: "hsl(var(--primary-foreground))",
          },
          secondary: {
            DEFAULT: "hsl(var(--secondary))",
            foreground: "hsl(var(--secondary-foreground))",
          },
          destructive: {
            DEFAULT: "hsl(var(--destructive))",
            foreground: "hsl(var(--destructive-foreground))",
          },
          muted: {
            DEFAULT: "hsl(var(--muted))",
            foreground: "hsl(var(--muted-foreground))",
          },
          accent: {
            DEFAULT: "hsl(var(--accent))",
            foreground: "hsl(var(--accent-foreground))",
          },
          popover: {
            DEFAULT: "hsl(var(--popover))",
            foreground: "hsl(var(--popover-foreground))",
          },
          card: {
            DEFAULT: "hsl(var(--card))",
            foreground: "hsl(var(--card-foreground))",
          },
          chart: {
            "1": "hsl(var(--chart-1))",
            "2": "hsl(var(--chart-2))",
            "3": "hsl(var(--chart-3))",
            "4": "hsl(var(--chart-4))",
            "5": "hsl(var(--chart-5))",
          },
          // UI specific colors
          ui: {
            bg: {
              base: "hsl(var(--ui-bg-base))",
              subtle: "hsl(var(--ui-bg-subtle))",
              component: "hsl(var(--ui-bg-component))",
              field: "hsl(var(--ui-bg-field))",
              highlight: "hsl(var(--ui-bg-highlight))",
              interactive: "hsl(var(--ui-bg-interactive))",
              disabled: "hsl(var(--ui-bg-disabled))",
              overlay: "hsl(var(--ui-bg-overlay))",
              hover: {
                base: "hsl(var(--ui-bg-base-hover))",
                subtle: "hsl(var(--ui-bg-subtle-hover))",
                component: "hsl(var(--ui-bg-component-hover))",
                field: "hsl(var(--ui-bg-field-hover))",
                highlight: "hsl(var(--ui-bg-highlight-hover))",
              },
              pressed: {
                base: "hsl(var(--ui-bg-base-pressed))",
                subtle: "hsl(var(--ui-bg-subtle-pressed))",
                component: "hsl(var(--ui-bg-component-pressed))",
              },
            },
            fg: {
              base: "hsl(var(--ui-fg-base))",
              subtle: "hsl(var(--ui-fg-subtle))",
              muted: "hsl(var(--ui-fg-muted))",
              disabled: "hsl(var(--ui-fg-disabled))",
              error: "hsl(var(--ui-fg-error))",
              interactive: "hsl(var(--ui-fg-interactive))",
              "interactive-hover": "hsl(var(--ui-fg-interactive-hover))",
              "on-color": "hsl(var(--ui-fg-on-color))",
              "on-inverted": "hsl(var(--ui-fg-on-inverted))",
            },
            border: {
              base: "hsl(var(--ui-border-base))",
              strong: "hsl(var(--ui-border-strong))",
              interactive: "hsl(var(--ui-border-interactive))",
              error: "hsl(var(--ui-border-error))",
              danger: "hsl(var(--ui-border-danger))",
              transparent: "hsl(var(--ui-border-transparent))",
            },
          },
        },
        borderRadius: {
          lg: "var(--radius)",
          md: "calc(var(--radius) - 2px)",
          sm: "calc(var(--radius) - 4px)",
        },
        fontFamily: {
          sans: FONT_FAMILY_SANS,
          mono: FONT_FAMILY_MONO,
        },
        transitionProperty: {
          fg: "color, background-color, border-color, box-shadow, opacity",
        },
        keyframes: {
          "accordion-down": {
            from: { height: "0px" },
            to: { height: "var(--radix-accordion-content-height)" },
          },
          "accordion-up": {
            from: { height: "var(--radix-accordion-content-height)" },
            to: { height: "0px" },
          },
        },
        animation: {
          "accordion-down": "accordion-down 0.2s ease-out",
          "accordion-up": "accordion-up 0.2s ease-out",
        },
        boxShadow: {
          "borders-interactive-with-active":
            "var(--shadow-borders-interactive-with-active)",
          "buttons-danger-focus": "var(--shadow-buttons-danger-focus)",
          "details-contrast-on-bg-interactive":
            "var(--shadow-details-contrast-on-bg-interactive)",
          "borders-interactive-with-focus":
            "var(--shadow-borders-interactive-with-focus)",
          "borders-error": "var(--shadow-borders-error)",
          "borders-focus": "var(--shadow-borders-focus)",
          "borders-interactive-with-shadow":
            "var(--shadow-borders-interactive-with-shadow)",
          "buttons-danger": "var(--shadow-buttons-danger)",
          "buttons-inverted-focus": "var(--shadow-buttons-inverted-focus)",
          "elevation-card-hover": "var(--shadow-elevation-card-hover)",
          "details-switch-handle": "var(--shadow-details-switch-handle)",
          "buttons-neutral": "var(--shadow-buttons-neutral)",
          "borders-base": "var(--shadow-borders-base)",
          "elevation-card-rest": "var(--shadow-elevation-card-rest)",
          "buttons-neutral-focus": "var(--shadow-buttons-neutral-focus)",
          "details-switch-background-focus":
            "var(--shadow-details-switch-background-focus)",
          "details-switch-background":
            "var(--shadow-details-switch-background)",
          "elevation-flyout": "var(--shadow-elevation-flyout)",
          "elevation-tooltip": "var(--shadow-elevation-tooltip)",
          "elevation-modal": "var(--shadow-elevation-modal)",
          "elevation-code-block": "var(--shadow-elevation-code-block)",
          "buttons-inverted": "var(--shadow-buttons-inverted)",
          "elevation-commandbar": "var(--shadow-elevation-commandbar)",
        },
      },
    },
  },
);
