@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Colors - Background */
    --background: 255 255 255;
    --foreground: 24 24 27;
    --card: 250 250 250;
    --card-foreground: 24 24 27;
    --popover: 255 255 255;
    --popover-foreground: 24 24 27;

    /* Colors - Primary */
    --primary: 59 130 246;
    --primary-foreground: 255 255 255;

    /* Colors - Secondary */
    --secondary: 244 244 245;
    --secondary-foreground: 24 24 27;

    /* Colors - Muted */
    --muted: 250 250 250;
    --muted-foreground: 113 113 122;

    /* Colors - Accent */
    --accent: 244 244 245;
    --accent-foreground: 24 24 27;

    /* Colors - Destructive */
    --destructive: 225 29 72;
    --destructive-foreground: 255 255 255;

    /* Colors - Border */
    --border: 228 228 231;
    --input: 228 228 231;

    /* Colors - Ring */
    --ring: 59 130 246;

    /* Colors - Chart */
    --chart-1: 59 130 246;
    --chart-2: 16 185 129;
    --chart-3: 249 115 22;
    --chart-4: 244 63 94;
    --chart-5: 167 139 250;

    /* Radius */
    --radius: 0.5rem;

    /* UI Specific Colors */
    --ui-bg-base: 255 255 255;
    --ui-bg-subtle: 250 250 250;
    --ui-bg-component: 250 250 250;
    --ui-bg-field: 250 250 250;
    --ui-bg-highlight: 239 246 255;
    --ui-bg-interactive: 59 130 246;
    --ui-bg-disabled: 244 244 245;
    --ui-bg-overlay: 24 24 27 / 0.4;

    /* UI Background Hover States */
    --ui-bg-base-hover: 244 244 245;
    --ui-bg-subtle-hover: 244 244 245;
    --ui-bg-component-hover: 244 244 245;
    --ui-bg-field-hover: 244 244 245;
    --ui-bg-highlight-hover: 219 234 254;

    /* UI Background Pressed States */
    --ui-bg-base-pressed: 228 228 231;
    --ui-bg-subtle-pressed: 228 228 231;
    --ui-bg-component-pressed: 228 228 231;

    /* UI Foreground Colors */
    --ui-fg-base: 24 24 27;
    --ui-fg-subtle: 82 82 91;
    --ui-fg-muted: 113 113 122;
    --ui-fg-disabled: 161 161 170;
    --ui-fg-error: 225 29 72;
    --ui-fg-interactive: 59 130 246;
    --ui-fg-interactive-hover: 37 99 235;
    --ui-fg-on-color: 255 255 255;
    --ui-fg-on-inverted: 255 255 255;

    /* UI Border Colors */
    --ui-border-base: 228 228 231;
    --ui-border-strong: 212 212 216;
    --ui-border-interactive: 59 130 246;
    --ui-border-error: 225 29 72;
    --ui-border-danger: 190 18 60;
    --ui-border-transparent: 255 255 255 / 0;

    /* UI Button Colors */
    --ui-button-neutral: 255 255 255;
    --ui-button-neutral-hover: 244 244 245;
    --ui-button-neutral-pressed: 228 228 231;
    --ui-button-inverted: 39 39 42;
    --ui-button-inverted-hover: 63 63 70;
    --ui-button-inverted-pressed: 82 82 91;
    --ui-button-danger: 225 29 72;
    --ui-button-danger-hover: 190 18 60;
    --ui-button-danger-pressed: 159 18 57;
    --ui-button-transparent: 255 255 255 / 0;
    --ui-button-transparent-hover: 244 244 245;
    --ui-button-transparent-pressed: 228 228 231;

    /* UI Tag Colors */
    --ui-tag-neutral-bg: 244 244 245;
    --ui-tag-neutral-bg-hover: 228 228 231;
    --ui-tag-neutral-text: 82 82 91;
    --ui-tag-neutral-border: 228 228 231;
    --ui-tag-neutral-icon: 161 161 170;

    --ui-tag-red-bg: 255 228 230;
    --ui-tag-red-bg-hover: 254 205 211;
    --ui-tag-red-text: 159 18 57;
    --ui-tag-red-border: 254 205 211;
    --ui-tag-red-icon: 244 63 94;

    --ui-tag-blue-bg: 219 234 254;
    --ui-tag-blue-bg-hover: 191 219 254;
    --ui-tag-blue-text: 30 64 175;
    --ui-tag-blue-border: 191 219 254;
    --ui-tag-blue-icon: 96 165 250;

    --ui-tag-green-bg: 209 250 229;
    --ui-tag-green-bg-hover: 167 243 208;
    --ui-tag-green-text: 6 95 70;
    --ui-tag-green-border: 167 243 208;
    --ui-tag-green-icon: 16 185 129;

    --ui-tag-orange-bg: 255 237 213;
    --ui-tag-orange-bg-hover: 254 215 170;
    --ui-tag-orange-text: 154 52 18;
    --ui-tag-orange-border: 254 215 170;
    --ui-tag-orange-icon: 249 115 22;

    --ui-tag-purple-bg: 237 233 254;
    --ui-tag-purple-bg-hover: 221 214 254;
    --ui-tag-purple-text: 91 33 182;
    --ui-tag-purple-border: 221 214 254;
    --ui-tag-purple-icon: 167 139 250;

    /* UI Switch Colors */
    --ui-switch-off: 228 228 231;
    --ui-switch-off-hover: 212 212 216;

    /* UI Alpha Colors */
    --ui-alpha-250: 24 24 27 / 0.1;
    --ui-alpha-400: 24 24 27 / 0.24;

    /* UI Contrast Colors */
    --ui-contrast-bg-base: 24 24 27;
    --ui-contrast-bg-base-hover: 39 39 42;
    --ui-contrast-bg-base-pressed: 63 63 70;
    --ui-contrast-bg-subtle: 39 39 42;
    --ui-contrast-fg-primary: 255 255 255 / 0.88;
    --ui-contrast-fg-secondary: 255 255 255 / 0.56;
    --ui-contrast-border-base: 255 255 255 / 0.15;
    --ui-contrast-border-top: 24 24 27;
    --ui-contrast-border-bot: 255 255 255 / 0.1;

    /* UI Menu Colors */
    --ui-menu-border-top: 228 228 231;
    --ui-menu-border-bot: 255 255 255;

    /* UI Field Component Colors */
    --ui-field-component: 255 255 255;
    --ui-field-component-hover: 250 250 250;
  }

  .dark {
    /* Colors - Background */
    --background: 24 24 27;
    --foreground: 244 244 245;
    --card: 39 39 42;
    --card-foreground: 244 244 245;
    --popover: 39 39 42;
    --popover-foreground: 244 244 245;

    /* Colors - Primary */
    --primary: 96 165 250;
    --primary-foreground: 255 255 255;

    /* Colors - Secondary */
    --secondary: 39 39 42;
    --secondary-foreground: 244 244 245;

    /* Colors - Muted */
    --muted: 39 39 42;
    --muted-foreground: 161 161 170;

    /* Colors - Accent */
    --accent: 39 39 42;
    --accent-foreground: 244 244 245;

    /* Colors - Destructive */
    --destructive: 251 113 133;
    --destructive-foreground: 255 255 255;

    /* Colors - Border */
    --border: 255 255 255 / 0.08;
    --input: 255 255 255 / 0.08;

    /* Colors - Ring */
    --ring: 96 165 250;

    /* UI Specific Colors */
    --ui-bg-base: 33 33 36;
    --ui-bg-subtle: 24 24 27;
    --ui-bg-component: 39 39 42;
    --ui-bg-field: 255 255 255 / 0.04;
    --ui-bg-highlight: 23 37 84;
    --ui-bg-interactive: 96 165 250;
    --ui-bg-disabled: 39 39 42;
    --ui-bg-overlay: 24 24 27 / 0.72;

    /* UI Background Hover States */
    --ui-bg-base-hover: 39 39 42;
    --ui-bg-subtle-hover: 33 33 36;
    --ui-bg-component-hover: 255 255 255 / 0.1;
    --ui-bg-field-hover: 255 255 255 / 0.08;
    --ui-bg-highlight-hover: 30 58 138;

    /* UI Background Pressed States */
    --ui-bg-base-pressed: 63 63 70;
    --ui-bg-subtle-pressed: 39 39 42;
    --ui-bg-component-pressed: 255 255 255 / 0.16;

    /* UI Foreground Colors */
    --ui-fg-base: 244 244 245;
    --ui-fg-subtle: 161 161 170;
    --ui-fg-muted: 113 113 122;
    --ui-fg-disabled: 82 82 91;
    --ui-fg-error: 251 113 133;
    --ui-fg-interactive: 96 165 250;
    --ui-fg-interactive-hover: 147 197 253;
    --ui-fg-on-color: 255 255 255;
    --ui-fg-on-inverted: 24 24 27;

    /* UI Border Colors */
    --ui-border-base: 255 255 255 / 0.08;
    --ui-border-strong: 255 255 255 / 0.16;
    --ui-border-interactive: 96 165 250;
    --ui-border-error: 251 113 133;
    --ui-border-danger: 190 18 60;
    --ui-border-transparent: 255 255 255 / 0;

    /* UI Button Colors */
    --ui-button-neutral: 255 255 255 / 0.04;
    --ui-button-neutral-hover: 255 255 255 / 0.08;
    --ui-button-neutral-pressed: 255 255 255 / 0.12;
    --ui-button-inverted: 82 82 91;
    --ui-button-inverted-hover: 113 113 122;
    --ui-button-inverted-pressed: 161 161 170;
    --ui-button-danger: 159 18 57;
    --ui-button-danger-hover: 190 18 60;
    --ui-button-danger-pressed: 225 29 72;
    --ui-button-transparent: 255 255 255 / 0;
    --ui-button-transparent-hover: 255 255 255 / 0.08;
    --ui-button-transparent-pressed: 255 255 255 / 0.12;

    /* UI Tag Colors - Dark Mode */
    --ui-tag-neutral-bg: 255 255 255 / 0.08;
    --ui-tag-neutral-bg-hover: 255 255 255 / 0.12;
    --ui-tag-neutral-text: 212 212 216;
    --ui-tag-neutral-border: 255 255 255 / 0.06;
    --ui-tag-neutral-icon: 113 113 122;

    --ui-tag-red-bg: 76 5 25;
    --ui-tag-red-bg-hover: 136 19 55;
    --ui-tag-red-text: 253 164 175;
    --ui-tag-red-border: 136 19 55;
    --ui-tag-red-icon: 251 113 133;

    --ui-tag-blue-bg: 23 37 84;
    --ui-tag-blue-bg-hover: 30 58 138;
    --ui-tag-blue-text: 147 197 253;
    --ui-tag-blue-border: 30 58 138;
    --ui-tag-blue-icon: 96 165 250;

    --ui-tag-green-bg: 2 44 34;
    --ui-tag-green-bg-hover: 6 78 59;
    --ui-tag-green-text: 52 211 153;
    --ui-tag-green-border: 6 78 59;
    --ui-tag-green-icon: 16 185 129;

    --ui-tag-orange-bg: 67 20 7;
    --ui-tag-orange-bg-hover: 124 45 18;
    --ui-tag-orange-text: 253 186 116;
    --ui-tag-orange-border: 124 45 18;
    --ui-tag-orange-icon: 251 146 60;

    --ui-tag-purple-bg: 46 16 101;
    --ui-tag-purple-bg-hover: 91 33 182;
    --ui-tag-purple-text: 196 181 253;
    --ui-tag-purple-border: 91 33 182;
    --ui-tag-purple-icon: 167 139 250;

    /* UI Switch Colors - Dark Mode */
    --ui-switch-off: 63 63 70;
    --ui-switch-off-hover: 82 82 91;

    /* UI Alpha Colors - Dark Mode */
    --ui-alpha-250: 255 255 255 / 0.1;
    --ui-alpha-400: 255 255 255 / 0.24;

    /* UI Contrast Colors - Dark Mode */
    --ui-contrast-bg-base: 39 39 42;
    --ui-contrast-bg-base-hover: 63 63 70;
    --ui-contrast-bg-base-pressed: 82 82 91;
    --ui-contrast-bg-subtle: 255 255 255 / 0.04;
    --ui-contrast-fg-primary: 255 255 255 / 0.88;
    --ui-contrast-fg-secondary: 255 255 255 / 0.56;
    --ui-contrast-border-base: 255 255 255 / 0.16;
    --ui-contrast-border-top: 33 33 36;
    --ui-contrast-border-bot: 255 255 255 / 0.08;

    /* UI Menu Colors - Dark Mode */
    --ui-menu-border-top: 33 33 36;
    --ui-menu-border-bot: 255 255 255 / 0.08;

    /* UI Field Component Colors - Dark Mode */
    --ui-field-component: 33 33 36;
    --ui-field-component-hover: 39 39 42;
  }
}
