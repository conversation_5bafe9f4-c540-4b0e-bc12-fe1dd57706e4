{"name": "@saf/ui-preset", "version": "1.0.0", "description": "SAF UI preset - A self-managed design system with CSS variables and Tailwind configuration", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/saf-frontend.git", "directory": "packages/ui-preset"}, "author": "SAF Team", "exports": {".": "./src/index.ts", "./styles/base.css": "./src/styles/base.css", "./styles/shadows.css": "./src/styles/shadows.css", "./package.json": "./package.json"}, "main": "./src/index.ts", "dependencies": {"@tailwindcss/forms": "^0.5.3", "tailwindcss-animate": "^1.0.7"}, "peerDependencies": {"tailwindcss": ">=3.0.0"}, "devDependencies": {"tailwindcss": "^3.4.1", "typescript": "^5.1.6"}, "publishConfig": {"access": "public"}}