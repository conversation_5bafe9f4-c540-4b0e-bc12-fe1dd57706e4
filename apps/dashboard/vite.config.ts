import { defineConfig } from "vite"
import react from "@vitejs/plugin-react-swc"
import { fileURLToPath } from "url"
import path, { dirname } from "path"

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: [
      {
        find: "./runtimeConfig",
        replacement: "./runtimeConfig.browser",
      },
      {
        find: "@",
        replacement: path.resolve(__dirname, "./src"),
      },
      // See https://github.com/mantinedev/ui.mantine.dev/issues/113
      {
        find: "@tabler/icons-react",
        replacement: "@tabler/icons-react/dist/esm/icons/index.mjs",
      },
      // Reference: https://github.com/vercel/turbo/discussions/620#discussioncomment-2136195
      {
        find: "@ui/blocks",
        replacement: path.resolve(__dirname, "../../packages/ui/src/blocks"),
      },
      {
        find: "@ui/components",
        replacement: path.resolve(__dirname, "../../packages/ui/src/components"),
      },
      {
        find: "@ui/providers",
        replacement: path.resolve(__dirname, "../../packages/ui/src/providers"),
      },
      {
        find: "@ui/hooks",
        replacement: path.resolve(__dirname, "../../packages/ui/src/hooks"),
      },
      {
        find: "@ui/utils",
        replacement: path.resolve(__dirname, "../../packages/ui/src/utils"),
      },
      {
        find: "@ui/types",
        replacement: path.resolve(__dirname, "../../packages/ui/src/types"),
      },
      {
        find: "@ui-preset",
        replacement: path.resolve(__dirname, "../../packages/ui-preset"),
      },
    ],
  },
})
