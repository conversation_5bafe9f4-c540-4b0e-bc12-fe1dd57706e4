import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { JsonView } from "@/components/common/json-view-section/json-view-section"
import { Loader } from "@/components/common/loader"
import { i18n } from "@/i18n/i18n"
import { showHumanFriendlyError } from "@/lib/is-validation-error"
import { convertRecordValuesToString, keyValueArrayToRecord, parseJSONValues, recordToKeyValueArray } from "@/lib/utils"
import { zodResolver } from "@hookform/resolvers/zod"
import { InformationCircle } from "@medusajs/icons"
import { components } from "@saf/sdk"
import { Alert, Badge, Button, Input, Select, Tabs, Text, toast, Tooltip } from "@saf/ui"
import { IconPointFilled } from "@tabler/icons-react"
import React, { useCallback, useEffect, useRef, useState } from "react"
import { useForm, useFormContext } from "react-hook-form"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"
import { z } from "zod"
import { removePathParam } from "../../components/external-api-create-form"
import { defaultKeyValue, keyValueArraySchema, KeyValuePairEditor } from "../../components/key-value-pair-editor"
import { usePathParameterDetection } from "../../hooks/use-path-parameter-detection"
// import JSONEditor from "@uiw/react-json-view"
import JSONEditor from "@uiw/react-json-view/editor"

const apiEndpointCreateFormSchema = z.object({
  name: z.string().min(1, { message: i18n.t("validation.required") }),
  urlPath: z.string().min(1, { message: i18n.t("validation.required") }),
  httpMethod: z.enum(["get", "post", "put", "delete", "patch", "options", "head", "connect", "trace"]),
  headers: keyValueArraySchema.optional(),
  routeParameters: keyValueArraySchema.optional(),
  queryParameters: keyValueArraySchema.optional(),
  body: keyValueArraySchema.optional(),
  cookies: keyValueArraySchema.optional(),
  savedResponse: z.any().optional(),
})

type ApiEndpointResponse = components["schemas"]["ExternalApiResponse"]

export const ApiEndpointCreateForm = ({
  data,
  baseUrl,
  onSuccess,
}: {
  data?: ApiEndpointResponse
  baseUrl?: string
  onSuccess?: (data: ApiEndpointResponse) => void
}) => {
  const formRef = useRef<HTMLFormElement | null>(null)
  const { teamId = "", miniAppId = "", dataSourceId = "" } = useParams()
  const { t } = useTranslation()
  const [preview, setPreview] = React.useState(false)
  const [activeTab, setActiveTab] = useState("header")

  const getFormDataWithDefaultValue = useCallback((newData?: ApiEndpointResponse) => {
    const config = newData?.config
    return {
      name: newData?.name || "",
      urlPath: newData?.urlPath || "",
      httpMethod: newData?.httpMethod || "get",
      headers: [...recordToKeyValueArray(config?.headers), defaultKeyValue],
      routeParameters: [...recordToKeyValueArray(config?.routeParameters)],
      queryParameters: [...recordToKeyValueArray(config?.queryParameters), defaultKeyValue],
      body: [...recordToKeyValueArray(convertRecordValuesToString(config?.body || {})), defaultKeyValue],
      cookies: [...recordToKeyValueArray(config?.cookies), defaultKeyValue],
      savedResponse: newData?.savedResponse || "",
    }
  }, [])

  const form = useForm<z.infer<typeof apiEndpointCreateFormSchema>>({
    resolver: zodResolver(apiEndpointCreateFormSchema),
    defaultValues: getFormDataWithDefaultValue(data),
  })

  useEffect(() => {
    if (data) {
      form.reset(getFormDataWithDefaultValue(data))
    }
  }, [form, data, getFormDataWithDefaultValue])

  const { mutateAsync: updateMutation, isPending: isUpdatePending } = safQuery.useMutation(
    "patch",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis/{externalApiId}",
  )

  const { mutateAsync: createMutation, isPending: isCreatePending } = safQuery.useMutation(
    "post",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis",
  )

  const snapshotQuery = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis/{externalApiId}/execute",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
          miniAppId: parseInt(miniAppId),
          dataSourceId: parseInt(dataSourceId || ""),
          externalApiId: parseInt(`${data?.id}` || ""),
        },
      },
    },
    {
      enabled: !!data && preview,
    },
  )

  const isPending = isUpdatePending || isCreatePending

  const handleSubmit = form.handleSubmit(async (values) => {
    try {
      const newData:
        | components["schemas"]["CreateExternalApiRequest"]
        | components["schemas"]["UpdateExternalApiRequest"] = {
        name: values.name || "",
        urlPath: values.urlPath,
        httpMethod: values.httpMethod,
        config: {
          headers: keyValueArrayToRecord(values.headers),
          routeParameters: keyValueArrayToRecord(values.routeParameters),
          queryParameters: keyValueArrayToRecord(values.queryParameters),
          body: parseJSONValues(keyValueArrayToRecord(values.body) ?? {}),
          cookies: keyValueArrayToRecord(values.cookies),
        },
        savedResponse: values.savedResponse,
      }

      let response: ApiEndpointResponse

      if (data != null) {
        response = await updateMutation({
          params: {
            path: {
              teamId: parseInt(teamId),
              miniAppId: parseInt(miniAppId),
              dataSourceId: parseInt(dataSourceId || ""),
              externalApiId: data?.id,
            },
          },
          body: newData,
        })
      } else {
        response = await createMutation({
          params: {
            path: {
              teamId: parseInt(teamId),
              miniAppId: parseInt(miniAppId),
              dataSourceId: parseInt(dataSourceId || ""),
            },
          },
          body: newData as components["schemas"]["CreateExternalApiRequest"],
        })
      }
      toast.success(data ? t("operationFeedback.updateSuccess") : t("operationFeedback.createSuccess"))

      // Invalidate queries to refresh the data
      queryClient.invalidateQueries(
        safQuery.queryOptions(
          "get",
          "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis",
          {
            params: {
              path: {
                teamId: parseInt(teamId),
                miniAppId: parseInt(miniAppId),
                dataSourceId: parseInt(dataSourceId || ""),
              },
            },
          },
        ),
      )

      if (data) {
        queryClient.invalidateQueries(
          safQuery.queryOptions(
            "get",
            "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis/{externalApiId}",
            {
              params: {
                path: {
                  teamId: parseInt(teamId),
                  miniAppId: parseInt(miniAppId),
                  dataSourceId: parseInt(dataSourceId || ""),
                  externalApiId: data?.id,
                },
              },
            },
          ),
        )
      }

      onSuccess?.(response)
    } catch (error) {
      toast.error(showHumanFriendlyError(error))
    }
  })

  const handleSaveResponse = useCallback(() => {
    try {
      if (!snapshotQuery.data) return

      const formattedResponse = JSON.stringify(snapshotQuery.data, null, 2)

      form.setValue("savedResponse", formattedResponse)

      formRef.current?.requestSubmit()

      // Use the state setter to change the tab programmatically
      setActiveTab("savedResponse")
    } catch (error) {
      toast.error(showHumanFriendlyError(error))
    }
  }, [snapshotQuery.data, form, setActiveTab])

  // Parse JSON for the editor
  const getSavedResponseAsJson = () => {
    try {
      const savedResponse = form.watch("savedResponse")
      return savedResponse ? JSON.parse(savedResponse) : {}
    } catch (error) {
      return {}
    }
  }

  // Handle JSON editor changes
  const handleJsonChange = (json: any) => {
    try {
      const formattedJson = JSON.stringify(json, null, 2)
      form.setValue("savedResponse", formattedJson)
    } catch (error) {
      toast.error("Invalid JSON format")
    }
  }

  return (
    <div className="flex h-full flex-col divide-y overflow-hidden lg:flex-row lg:divide-x lg:divide-y-0">
      <Form {...form}>
        <form
          ref={formRef}
          onSubmit={handleSubmit}
          className="flex h-1/2 flex-1 shrink-0 flex-col divide-y overflow-hidden lg:h-full"
        >
          {form.formState.errors.root && (
            <Alert variant="error" dismissible={false} className="text-balance">
              {form.formState.errors.root.message}
            </Alert>
          )}
          <div className="flex h-[var(--topbar-height)] items-center space-y-2 px-4 py-2">
            <div className="flex flex-1 items-start gap-2">
              <Field {...form} name="name" className="flex-1" hideErrorMessage>
                <Input
                  className="border-none bg-ui-bg-field text-lg font-medium shadow-none placeholder:text-lg placeholder:font-normal"
                  placeholder="Endpoint Name"
                />
              </Field>
              <Button type="submit" isLoading={isPending}>
                {t("actions.save")}
              </Button>
            </div>
          </div>
          <div className="flex items-start gap-2 px-4 pb-4 pt-4">
            <div className="w-[96px]">
              <Field {...form} name="httpMethod">
                <Select variant="pop" onValueChange={form.setValue.bind(null, "httpMethod")}>
                  <Select.Trigger>
                    <Select.Value />
                  </Select.Trigger>
                  <Select.Content>
                    {["get", "post", "put", "delete", "patch", "options", "head", "connect", "trace"].map((item) => (
                      <Select.Item key={item} value={item}>
                        {item.toUpperCase()}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </Field>
            </div>
            <div className="flex flex-1 gap-2">
              <Tooltip content={baseUrl || "No base URL set"}>
                <Badge>
                  Base URL
                  <InformationCircle className="ms-1" />
                </Badge>
              </Tooltip>
              <Field {...form} name="urlPath" className="flex-1">
                <Input variant="pop" placeholder="/categories" className="w-full max-w-none" />
              </Field>
            </div>
            <div>
              <Button
                type="button"
                variant="secondary"
                onClick={() => {
                  setPreview(true)
                  handleSubmit().then(() => snapshotQuery.refetch())
                }}
                isLoading={snapshotQuery.isLoading}
              >
                Run
              </Button>
            </div>
          </div>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex flex-1 flex-col overflow-hidden">
            <TabList />

            <TabContainer value="header">
              <KeyValuePairEditor
                name="headers"
                control={form.control}
                emptyMessage="No headers added. Click 'Add Header' to add one."
                addButtonText="Add Header"
                defaultValue={defaultKeyValue}
              />
            </TabContainer>

            <TabContainer value="path">
              <PathParameterValueEditor />
            </TabContainer>

            <TabContainer value="params">
              <KeyValuePairEditor
                name="queryParameters"
                control={form.control}
                emptyMessage="No query parameters added. Click 'Add More' to add one."
                addButtonText="Add Parameter"
                defaultValue={defaultKeyValue}
              />
            </TabContainer>

            <TabContainer value="body">
              <KeyValuePairEditor
                name="body"
                control={form.control}
                emptyMessage="No body fields added. Click 'Add More' to add one."
                addButtonText="Add Field"
                defaultValue={defaultKeyValue}
              />
            </TabContainer>

            <TabContainer value="cookie">
              <KeyValuePairEditor
                name="cookies"
                control={form.control}
                emptyMessage="No cookies added. Click 'Add More' to add one."
                addButtonText="Add Cookie"
                defaultValue={defaultKeyValue}
              />
            </TabContainer>

            <TabContainer value="savedResponse">
              <div className="space-y-2">
                <Text size="xsmall" className="text-ui-fg-subtle">
                  Define a mock response that will be returned when using the "Use Saved Response" option
                </Text>
                <Field {...form} name="savedResponse">
                  <JSONEditor
                    value={getSavedResponseAsJson()}
                    onChange={handleJsonChange}
                    className="min-h-[200px] font-mono"
                  />
                </Field>
              </div>
            </TabContainer>
          </Tabs>
        </form>
      </Form>
      {preview && (
        <div className="h-1/2 flex-1 lg:h-full">
          <div className="flex h-10 w-full items-center gap-4 border-b px-2">
            {snapshotQuery.data && (
              <div className="flex items-center gap-2">
                <Text size="small">Status code</Text> <Badge size="small">{snapshotQuery.data?.statusCode}</Badge>
              </div>
            )}
            <Button size="small" onClick={handleSaveResponse} disabled={!snapshotQuery.data}>
              Save Response
            </Button>
          </div>
          {(snapshotQuery.isLoading || snapshotQuery.isFetching) && (
            <div className="flex h-full items-center justify-center p-4">
              <Loader />
            </div>
          )}
          {snapshotQuery.isError && (
            <div className="flex h-full items-center justify-center p-4 text-ui-fg-error">
              {showHumanFriendlyError(snapshotQuery.error)}
            </div>
          )}
          {snapshotQuery.data && (
            <div className="h-[calc(100%-2.5rem)] overflow-auto bg-ui-contrast-bg-subtle pl-2">
              <JsonView data={snapshotQuery.data} />
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export const TabList = () => {
  const form = useFormContext<z.infer<typeof apiEndpointCreateFormSchema>>()
  const routeParameters = form.watch("routeParameters")
  const headers = form.watch("headers")
  const queryParameters = form.watch("queryParameters")
  const body = form.watch("body")
  const cookies = form.watch("cookies")
  const savedResponse = form.watch("savedResponse")

  const hasSomeValue = (keyValues?: { key: string; value: string }[]) => {
    return keyValues?.some((item) => item.key || item.value)
  }

  return (
    <Tabs.List className="w-full shrink-0 overflow-x-auto text-nowrap border-b px-3 py-2">
      <Tabs.Trigger value="header">
        Headers
        {hasSomeValue(headers) && <DotIndicator />}
      </Tabs.Trigger>
      <Tabs.Trigger value="path">
        Path Parameters
        {hasSomeValue(routeParameters) && <DotIndicator />}
      </Tabs.Trigger>
      <Tabs.Trigger value="params">
        Query Parameters
        {hasSomeValue(queryParameters) && <DotIndicator />}
      </Tabs.Trigger>
      <Tabs.Trigger value="body">
        Body
        {hasSomeValue(body) && <DotIndicator />}
      </Tabs.Trigger>
      <Tabs.Trigger value="cookie">
        Cookies
        {hasSomeValue(cookies) && <DotIndicator />}
      </Tabs.Trigger>
      <Tabs.Trigger value="savedResponse">
        Saved Response
        {savedResponse && <DotIndicator />}
      </Tabs.Trigger>
    </Tabs.List>
  )
}

const TabContainer = ({ children, value }: { className?: string; children: React.ReactNode; value: string }) => {
  return (
    <Tabs.Content value={value} className="overflow-y-auto p-4">
      {children}
    </Tabs.Content>
  )
}

const DotIndicator = () => {
  return <IconPointFilled className="size-4 text-ui-tag-red-icon" />
}

const PathParameterValueEditor = () => {
  const form = useFormContext<z.infer<typeof apiEndpointCreateFormSchema>>()

  usePathParameterDetection({ form, urlFieldName: "urlPath" })

  const routeParameters = form.watch("routeParameters")
  const hasrouteParameters = routeParameters && routeParameters.length > 0

  const removerouteParametersFromUrl = (index: number) => {
    const urlPath = form.getValues("urlPath")
    const updatedUrlPath = removePathParam(urlPath, index)
    form.setValue("urlPath", updatedUrlPath)
  }

  return (
    hasrouteParameters && (
      <KeyValuePairEditor
        name="routeParameters"
        control={form.control}
        disableKeyInput
        disableAddButton
        onRemove={(index) => {
          removerouteParametersFromUrl(index)
        }}
      />
    )
  )
}
